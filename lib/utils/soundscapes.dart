// Visual backgrounds that adapt to your app's current sound: rainy / waves / campfire.
// - SoundscapeBackground: pass `mode: Soundscape.rainy|waves|campfire`
// - Lightweight, battery-friendly Canvas effects; no assets or extra packages.
// - Works nicely behind a transparent Scaffold.
//
// Usage:
//   Stack(children:[
//     SoundscapeBackground(mode: Soundscape.rainy),
//     Scaffold(backgroundColor: Colors.transparent, body: ...),
//   ])

import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

enum Soundscape { rainy, waves, campfire }

class SoundscapeBackground extends StatelessWidget {
  const SoundscapeBackground({super.key, required this.mode, this.intensity = 1.0, this.oceanLevel = 0.46});
  final Soundscape mode;
  /// 0.5 ~ 1.5 recommended. Scales particle counts and amplitudes.
  final double intensity;
  final double oceanLevel;

  @override
  Widget build(BuildContext context) {
    switch (mode) {
      case Soundscape.rainy:
        return _RainySky(intensity: intensity);
      case Soundscape.waves:
        return _Ocean(intensity: intensity, oceanLevel: oceanLevel);
      case Soundscape.campfire:
        return _Campfire(intensity: intensity);
    }
  }
}

// ====== RAINY ==============================================================
class _RainySky extends StatelessWidget {
  const _RainySky({required this.intensity});
  final double intensity;
  @override
  Widget build(BuildContext context) {
    final top = const Color(0xFF0A101C);
    final bottom = const Color(0xFF0E1A2B);
    return Stack(children: [
      _GradientFill(top: top, bottom: bottom),
      _RainLayer(dropCount: (240 * intensity).round()),
      const _LowMist(),
    ]);
  }
}

class _RainLayer extends StatefulWidget {
  const _RainLayer({required this.dropCount});
  final int dropCount;
  @override
  State<_RainLayer> createState() => _RainLayerState();
}

class _Drop { Offset p; Offset v; double len; double thickness; _Drop(this.p, this.v, this.len, this.thickness); }
class _Ripple { Offset c; double r; double age; _Ripple(this.c, this.r, this.age); }

class _RainLayerState extends State<_RainLayer> with SingleTickerProviderStateMixin {
  late final AnimationController _ctrl;
  final rnd = Random();
  final drops = <_Drop>[];
  final ripples = <_Ripple>[];
  Size last = Size.zero;

  @override
  void initState(){
    super.initState();
    _ctrl = AnimationController(vsync:this, duration: const Duration(milliseconds: 16))..repeat();
  }
  @override
  void dispose(){ _ctrl.dispose(); super.dispose(); }

  void _ensure(Size size){
    if(size!=last || drops.isEmpty){
      drops
        ..clear()
        ..addAll(List.generate(widget.dropCount, (_){
          final x = rnd.nextDouble()*size.width;
          final y = rnd.nextDouble()*size.height;
          final speed = 600 + rnd.nextDouble()*500; // px/s
          final angle = pi*1.1; // slightly slanted
          final v = Offset(cos(angle), sin(angle))*speed;
          final len = 8 + rnd.nextDouble()*14;
          final th = 0.8 + rnd.nextDouble()*0.9;
          return _Drop(Offset(x,y), v, len, th);
        }));
      last = size;
    }
  }

  int _lastMs = DateTime.now().millisecondsSinceEpoch;
  @override
  Widget build(BuildContext context){
    return RepaintBoundary(
      child: CustomPaint(
        painter: _RainPainter(drops, ripples, baseCount: widget.dropCount, onEnsure: _ensure, repaint: _ctrl),
        size: Size.infinite,
      ),
    );
  }
}

class _RainPainter extends CustomPainter{
  _RainPainter(this.drops, this.ripples, {required this.baseCount, required this.onEnsure, required Listenable repaint}) : super(repaint: repaint);
  final int baseCount; final List<_Drop> drops; final List<_Ripple> ripples; final void Function(Size) onEnsure; final rnd = Random();
  double _burstClock = 0, _burstTimer = 0, _nextBurst = 8;
  int last = DateTime.now().millisecondsSinceEpoch;

  @override
  void paint(Canvas canvas, Size size){
    onEnsure(size);
    final now = DateTime.now().millisecondsSinceEpoch; final dt = (now-last)/1000.0; last = now;

    final p = Paint()
      ..color = Colors.white.withOpacity(0.35)
      ..strokeCap = StrokeCap.round;

    // burst scheduling and top-up to maintain drizzle with periodic bursts
    _burstClock += dt;
    if (_burstClock > _nextBurst) {
      _burstClock = 0;
      _nextBurst = 8 + rnd.nextDouble() * 6; // 8–14s
      _burstTimer = 1.6; // burst lasts ~1.6s
    }
    final target = baseCount + (_burstTimer > 0 ? (baseCount * 0.35).round() : 0);

    // top-up: keep creating new drops until we reach target
    while (drops.length < target) {
      final x = rnd.nextDouble() * size.width;
      final speed = 450 + rnd.nextDouble() * 350; // slower so it’s visible
      final angle = pi * 1.12;
      final v = Offset(cos(angle), sin(angle)) * speed;
      final len = 12 + rnd.nextDouble() * 18;
      final th  = 1.0 + rnd.nextDouble() * 0.6;
      drops.add(_Drop(Offset(x, -10), v, len, th));
    }
    if (_burstTimer > 0) _burstTimer -= dt;

    // update & draw drops
    for(final d in drops){
      final prev = d.p;
      d.p += d.v*dt;
      if(d.p.dy > size.height+20){
        // spawn ripple occasionally when hitting bottom
        if(rnd.nextDouble()<0.25){ ripples.add(_Ripple(Offset(d.p.dx, size.height-4), 2, 0)); }
        d.p = Offset(rnd.nextDouble()*size.width, -10);
      }
      p.strokeWidth = d.thickness;
      canvas.drawLine(d.p, d.p - d.v.normalized()*d.len, p);
    }

    // ripples
    final rp = Paint()..style = PaintingStyle.stroke;
    ripples.removeWhere((r){ r.age += dt; return r.age>0.9; });
    for(final r in ripples){
      final t = (1-r.age/0.9).clamp(0.0,1.0);
      rp.color = Colors.white.withOpacity(0.15*t);
      rp.strokeWidth = 1.0 + (1-t)*1.0;
      final rad = r.r + r.age*28;
      canvas.drawCircle(r.c, rad, rp);
    }
  }
  @override
  bool shouldRepaint(covariant _RainPainter oldDelegate)=>true;
}

class _LowMist extends StatefulWidget{ const _LowMist({super.key}); @override State<_LowMist> createState()=>_LowMistState(); }
class _LowMistState extends State<_LowMist> with SingleTickerProviderStateMixin{
  late final AnimationController _c; @override void initState(){ super.initState(); _c=AnimationController(vsync:this,duration:const Duration(seconds:6))..repeat(); }
  @override void dispose(){ _c.dispose(); super.dispose(); }
  @override Widget build(BuildContext context){
    return IgnorePointer(
      child: CustomPaint(painter:_MistPainter(repaint:_c), size: Size.infinite),
    );
  }
}
class _MistPainter extends CustomPainter{
  _MistPainter({required Listenable repaint}):super(repaint:repaint);
  @override
  void paint(Canvas canvas, Size size){
    final t = DateTime.now().millisecondsSinceEpoch/1000.0;
    final g = Paint()
      ..shader = ui.Gradient.linear(
        Offset(0,size.height*0.6), Offset(0,size.height),
        [Colors.white.withOpacity(0.02+0.02*(0.5+0.5*sin(t*0.5))), Colors.white.withOpacity(0.10)],
      );
    canvas.drawRect(Rect.fromLTWH(0,size.height*0.55,size.width,size.height*0.45), g);
  }
  @override bool shouldRepaint(covariant _MistPainter old)=>true;
}

// ====== OCEAN / WAVES ======================================================
class _Ocean extends StatelessWidget {
  const _Ocean({required this.intensity, this.oceanLevel = 0.46});
  final double intensity;
  final double oceanLevel;
  @override
  Widget build(BuildContext context) {
    return Stack(children:[
      const _GradientFill(top: Color(0xFF06121E), bottom: Color(0xFF0B2B3B)),
      _WaveLayer(lines: (3*intensity).clamp(2,6).toInt(), amp: 14*intensity, speed: 0.6+0.2*intensity, oceanLevel: oceanLevel),
      const _MoonGlow(),
    ]);
  }
}

class _WaveLayer extends StatefulWidget{ const _WaveLayer({required this.lines, required this.amp, required this.speed, this.oceanLevel = 0.33});
final int lines; final double amp; final double speed; final double oceanLevel;
@override State<_WaveLayer> createState()=>_WaveLayerState(); }
class _WaveLayerState extends State<_WaveLayer> with SingleTickerProviderStateMixin{
  late final AnimationController _c; @override void initState(){ super.initState(); _c=AnimationController(vsync:this,duration:const Duration(milliseconds:16))..repeat(); }
  @override void dispose(){ _c.dispose(); super.dispose(); }
  @override Widget build(BuildContext context){
    return RepaintBoundary(child: CustomPaint(painter:_WavesPainter(widget.lines, widget.amp, widget.speed, widget.oceanLevel, repaint:_c), size: Size.infinite));
  }
}
class _WavesPainter extends CustomPainter{
  _WavesPainter(this.lines, this.amp, this.speed, this.oceanLevel, {required Listenable repaint}):super(repaint:repaint);
  final int lines; final double amp; final double speed; final double oceanLevel;
  @override
  void paint(Canvas canvas, Size size){
    final t = DateTime.now().millisecondsSinceEpoch/1000.0;
    for(int i=0;i<lines;i++){
      final p = Path();
      final yBase = size.height * oceanLevel + i * 18;
      final a = amp*(1 - i/lines*0.5);
      final k = 2*pi/(size.width*0.9);
      p.moveTo(0, yBase);
      for(double x=0;x<=size.width;x+=8){
        final y = yBase + sin(k*x + t*(speed + i*0.12)) * a;
        p.lineTo(x, y);
      }
      p.lineTo(size.width, size.height);
      p.lineTo(0, size.height);
      p.close();
      final alpha = 0.18 - i*0.03;
      final color = i==0? const Color(0xFF9ADCF8).withOpacity(0.18): Colors.white.withOpacity(alpha.clamp(0.06, 0.18));
      final paint = Paint()..color=color..style=PaintingStyle.fill;
      canvas.drawPath(p, paint);
    }
  }
  @override bool shouldRepaint(covariant _WavesPainter old)=>true;
}
class _MoonGlow extends StatelessWidget{ const _MoonGlow({super.key}); @override Widget build(BuildContext context){
  return IgnorePointer(child: CustomPaint(size: Size.infinite, painter: _MoonPainter())); }}
class _MoonPainter extends CustomPainter{
  @override void paint(Canvas canvas, Size size){
    final center = Offset(size.width * 0.82, size.height * 0.13);
    final glow = Paint()
      ..color = Colors.white.withOpacity(0.12)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 26);
    canvas.drawCircle(center, 60, glow);
    final core = Paint()..color=Colors.white.withOpacity(0.8);
    canvas.drawCircle(center, 10, core);
    // reflection strip
    final strip = Rect.fromLTWH(size.width*0.68, size.height*0.58, size.width*0.22, 6);
    final grad = Paint()..shader = ui.Gradient.linear(strip.topLeft, strip.topRight, [Colors.white.withOpacity(0.0), Colors.white.withOpacity(0.4), Colors.white.withOpacity(0.0)]);
    canvas.drawRect(strip, grad);
  }
  @override bool shouldRepaint(covariant _MoonPainter old)=>false;
}

// ====== CAMPFIRE ===========================================================
class _Campfire extends StatelessWidget{
  const _Campfire({required this.intensity});
  final double intensity;
  @override Widget build(BuildContext context){
    return Stack(children:[
      const _GradientFill(top: Color(0xFF1A0C07), bottom: Color(0xFF2A120A)),
      /*Positioned(
        left: 0, right: 0, bottom: 0,
        child: IgnorePointer(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Image.asset(
              'assets/images/campfire_logs.png',
              width: MediaQuery.of(context).size.width * 0.55,
              fit: BoxFit.contain,
              errorBuilder: (_, __, ___) => const SizedBox.shrink(),
            ),
          ),
        ),
      ),*/
      _EmberLayer(count: (80 * intensity).round()),
      const _WarmFlicker(),
    ]);
  }
}

class _Ember { Offset p; Offset v; double r; double life; double age; _Ember(this.p,this.v,this.r,this.life,this.age); }
class _EmberLayer extends StatefulWidget{ const _EmberLayer({required this.count}); final int count; @override State<_EmberLayer> createState()=>_EmberLayerState(); }
class _EmberLayerState extends State<_EmberLayer> with SingleTickerProviderStateMixin{
  late final AnimationController _c; final rnd = Random(); final embers=<_Ember>[]; Size last=Size.zero;
  @override void initState(){ super.initState(); _c=AnimationController(vsync:this,duration:const Duration(milliseconds:16))..repeat(); }
  @override void dispose(){ _c.dispose(); super.dispose(); }
  void _ensure(Size s){
    if(s!=last || embers.isEmpty){
      embers
        ..clear()
        ..addAll(List.generate(widget.count, (_){
          final x = s.width*0.5 + (rnd.nextDouble()-0.5)*120;
          final y = s.height*0.85 + rnd.nextDouble()*20;
          final v = Offset((rnd.nextDouble()-0.5)*18, - (30 + rnd.nextDouble()*60));
          final r = 1.2 + rnd.nextDouble()*2.4;
          final life = 1.5 + rnd.nextDouble()*2.0;
          return _Ember(Offset(x,y), v, r, life, rnd.nextDouble()*life);
        }));
      last=s;
    }
  }
  @override Widget build(BuildContext context){
    return RepaintBoundary(child: CustomPaint(painter:_EmberPainter(embers,onEnsure:_ensure,repaint:_c), size: Size.infinite));
  }
}
class _EmberPainter extends CustomPainter{
  _EmberPainter(this.embers,{required this.onEnsure, required Listenable repaint}):super(repaint:repaint);
  final List<_Ember> embers; final void Function(Size) onEnsure; final rnd=Random(); int last=DateTime.now().millisecondsSinceEpoch;
  @override void paint(Canvas canvas, Size size){
    onEnsure(size);
    final now=DateTime.now().millisecondsSinceEpoch; final dt=(now-last)/1000.0; last=now;
    final halo = Paint()..maskFilter=const MaskFilter.blur(BlurStyle.normal, 8);
    final core = Paint();
    for(final e in embers){
      e.age += dt; if(e.age>e.life){
        // respawn near fire pit
        e.age=0; e.p = Offset(size.width*0.5 + (rnd.nextDouble()-0.5)*120, size.height*0.86 + rnd.nextDouble()*16);
        e.v = Offset((rnd.nextDouble()-0.5)*18, - (30 + rnd.nextDouble()*60));
      }
      e.p += e.v*dt; e.v = Offset(e.v.dx*0.98, e.v.dy - 8*dt); // buoyancy up
      final t = (1-e.age/e.life).clamp(0.0,1.0);
      final c = Color.lerp(const Color(0xFFFFC37A), const Color(0xFFFF5E2B), 1-t)!;
      halo.color = c.withOpacity(0.24 * t);
      core.color = c.withOpacity(0.9 * t);
      canvas.drawCircle(e.p, e.r*2.4, halo);
      canvas.drawCircle(e.p, e.r, core);
    }

    // simple campfire base glow
    final base = Paint()..shader = ui.Gradient.radial(Offset(size.width*0.5, size.height*0.9), 120, [const Color(0xFFFFA95C).withOpacity(0.25), Colors.transparent]);
    canvas.drawCircle(Offset(size.width*0.5, size.height*0.9), 120, base);
  }
  @override bool shouldRepaint(covariant _EmberPainter old)=>true;
}

class _WarmFlicker extends StatefulWidget{ const _WarmFlicker({super.key}); @override State<_WarmFlicker> createState()=>_WarmFlickerState(); }
class _WarmFlickerState extends State<_WarmFlicker> with SingleTickerProviderStateMixin{
  late final AnimationController _c; @override void initState(){ super.initState(); _c=AnimationController(vsync:this,duration:const Duration(milliseconds:600))..repeat(reverse:true); }
  @override void dispose(){ _c.dispose(); super.dispose(); }
  @override Widget build(BuildContext context){
    return AnimatedBuilder(animation:_c, builder: (_, __){
      final o = 0.03 + 0.02*(_c.value);
      return Container(color: Colors.black.withOpacity(o));
    });
  }
}

// ====== Common pieces ======================================================
class _GradientFill extends StatelessWidget{
  const _GradientFill({required this.top, required this.bottom});
  final Color top; final Color bottom;
  @override Widget build(BuildContext context){
    return Container(
      decoration: BoxDecoration(
          gradient: LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [top, bottom])
      ),
    );
  }
}

extension on Offset{ double get len=>sqrt(dx*dx+dy*dy); Offset normalized()=> len==0? this: this*(1/len); Offset operator +(Offset o)=>Offset(dx+o.dx,dy+o.dy); Offset operator -(Offset o)=>Offset(dx-o.dx,dy-o.dy); Offset operator *(double s)=>Offset(dx*s,dy*s); }


class _Logs extends StatelessWidget {
  const _Logs({super.key});
  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: CustomPaint(size: Size.infinite, painter: _LogsPainter()),
    );
  }
}

class _LogsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final cx = size.width * 0.5;
    final y  = size.height * 0.92;

    Paint logPaint(Color c) => Paint()
      ..color = c
      ..style = PaintingStyle.fill
      ..strokeWidth = 0;

    // shadows/glow
    final baseGlow = Paint()
      ..shader = ui.Gradient.radial(
        Offset(cx, y), 140,
        [const Color(0xFF7A3E12).withOpacity(0.22), Colors.transparent],
      );
    canvas.drawCircle(Offset(cx, y), 140, baseGlow);

    // two rounded logs, crossed
    final rrect = (Rect rect, double r) => RRect.fromRectAndRadius(rect, Radius.circular(r));
    final brown = const Color(0xFF5B3B22);
    final dark  = const Color(0xFF3F2817);

    // bottom-left → top-right
    canvas.save();
    canvas.translate(cx - 80, y - 12);
    canvas.rotate(-0.25);
    canvas.drawRRect(rrect(Rect.fromLTWH(0, 0, 160, 24), 12), logPaint(brown));
    canvas.drawRRect(rrect(Rect.fromLTWH(10, 4, 140, 16), 10), logPaint(dark));
    canvas.restore();

    // bottom-right → top-left
    canvas.save();
    canvas.translate(cx - 80, y - 12);
    canvas.rotate(0.25);
    canvas.drawRRect(rrect(Rect.fromLTWH(0, 0, 160, 24), 12), logPaint(brown));
    canvas.drawRRect(rrect(Rect.fromLTWH(10, 4, 140, 16), 10), logPaint(dark));
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant _LogsPainter oldDelegate) => false;
}
