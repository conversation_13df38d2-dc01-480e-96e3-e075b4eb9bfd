import 'package:flutter/material.dart';

class SoundSelectorCard extends StatelessWidget {
  const SoundSelectorCard({
    super.key,
    required this.value,              // 'rainy' | 'waves' | 'camp fire'
    required this.onChanged,
  });

  final String value;
  final void Function(String) onChanged;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;

    const items = <DropdownMenuItem<String>>[
      DropdownMenuItem(value: 'rainy',     child: Text('Rainy')),
      DropdownMenuItem(value: 'waves',     child: Text('Waves')),
      DropdownMenuItem(value: 'camp fire', child: Text('Camp Fire')),
    ];

    return Card(
      elevation: 0,
      color: cs.surfaceContainerHighest.withOpacity(0.55),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: DropdownButtonFormField<String>(
          value: value,
          isExpanded: true,
          items: items,
          onChanged: (v) { if (v != null) onChanged(v); },
          dropdownColor: cs.surface,
          icon: const Icon(Icons.keyboard_arrow_down_rounded),
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.music_note_rounded),
            labelText: 'Choose a sound',
            filled: true,
            fillColor: cs.surfaceContainerHighest.withOpacity(0.35),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(color: cs.outlineVariant),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(color: cs.outlineVariant),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(color: cs.primary, width: 2),
            ),
          ),
        ),
      ),
    );
  }
}
